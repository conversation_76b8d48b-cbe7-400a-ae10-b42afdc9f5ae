import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { videoApi, VideoFile, VideoList } from '@/lib/videoApi';
import { useToast } from '@/components/ui/use-toast';

export const useVideos = (page = 1, limit = 10) => {
  return useQuery({
    queryKey: ['videos', page, limit],
    queryFn: () => videoApi.listVideos(page, limit),
    staleTime: 2 * 60 * 1000,
  });
};

export const useVideo = (videoId: string) => {
  return useQuery({
    queryKey: ['video', videoId],
    queryFn: () => videoApi.getVideo(videoId),
    enabled: !!videoId,
    staleTime: 5 * 60 * 1000,
  });
};

export const usePlaybackInfo = (videoId: string) => {
  return useQuery({
    queryKey: ['playback', videoId],
    queryFn: () => videoApi.getPlaybackInfo(videoId),
    enabled: !!videoId,
    staleTime: 10 * 60 * 1000,
  });
};

export const useSearchVideos = (query: string, page = 1, limit = 10) => {
  return useQuery({
    queryKey: ['videos', 'search', query, page, limit],
    queryFn: () => videoApi.searchVideos(query, page, limit),
    enabled: query.length > 0,
    staleTime: 1 * 60 * 1000,
  });
};

export const useDeleteVideo = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: videoApi.deleteVideo,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['videos'] });
      toast({
        title: "Video deleted",
        description: "Video has been successfully deleted",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Delete failed",
        description: error.message || "Failed to delete video",
        variant: "destructive",
      });
    },
  });
};

export const useCreateJob = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: videoApi.createJob,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['videos'] });
      toast({
        title: "Job created",
        description: "Video processing job has been created",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Job creation failed",
        description: error.message || "Failed to create processing job",
        variant: "destructive",
      });
    },
  });
};
