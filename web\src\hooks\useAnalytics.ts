import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { analyticsApi, AnalyticsSummary, VideoPerformance } from '@/lib/analyticsApi';

export const useAnalyticsSummary = () => {
  return useQuery({
    queryKey: ['analytics', 'summary'],
    queryFn: analyticsApi.getAnalyticsSummary,
    staleTime: 2 * 60 * 1000,
    refetchInterval: 5 * 60 * 1000,
  });
};

export const useVideoPerformance = (videoId: string) => {
  return useQuery({
    queryKey: ['analytics', 'video', videoId],
    queryFn: () => analyticsApi.getVideoPerformance(videoId),
    enabled: !!videoId,
    staleTime: 5 * 60 * 1000,
  });
};

export const useTopPerformingVideos = (limit = 5) => {
  return useQuery({
    queryKey: ['analytics', 'top-videos', limit],
    queryFn: () => analyticsApi.getTopPerformingVideos(limit),
    staleTime: 5 * 60 * 1000,
  });
};

export const useRecentVideos = (limit = 5) => {
  return useQuery({
    queryKey: ['analytics', 'recent-videos', limit],
    queryFn: () => analyticsApi.getRecentVideos(limit),
    staleTime: 2 * 60 * 1000,
  });
};

export const useRecordVideoView = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ videoId, duration }: { videoId: string; duration: number }) =>
      analyticsApi.recordVideoView(videoId, duration),
    onSuccess: (_, { videoId }) => {
      queryClient.invalidateQueries({ queryKey: ['analytics'] });
      queryClient.invalidateQueries({ queryKey: ['analytics', 'video', videoId] });
    },
  });
};

export const useStartWatchSession = () => {
  return useMutation({
    mutationFn: analyticsApi.startWatchSession,
  });
};

export const useEndWatchSession = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ sessionId, watchDuration, completed }: {
      sessionId: string;
      watchDuration: number;
      completed: boolean;
    }) => analyticsApi.endWatchSession(sessionId, watchDuration, completed),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['analytics'] });
    },
  });
};
