{"version": 3, "sources": ["../../@vidstack/react/dev/chunks/vidstack-Breph70c.js"], "sourcesContent": ["\"use client\"\n\nimport { createScope, signal, effect, isString, deferredPromise, isObject, isNumber, isBoolean } from './vidstack-CH225ns1.js';\nimport { preconnect, TimeRange } from './vidstack-C-WrcxmD.js';\nimport { EmbedProvider } from './vidstack-CFWvJYI8.js';\nimport { resolveYouTubeVideoId } from './vidstack-Zc3I7oOd.js';\nimport 'react';\nimport '@floating-ui/dom';\n\nconst YouTubePlayerState = {\n  Unstarted: -1,\n  Ended: 0,\n  Playing: 1,\n  Paused: 2,\n  Buffering: 3,\n  Cued: 5\n};\n\nclass YouTubeProvider extends EmbedProvider {\n  $$PROVIDER_TYPE = \"YOUTUBE\";\n  scope = createScope();\n  #ctx;\n  #videoId = signal(\"\");\n  #state = -1;\n  #currentSrc = null;\n  #seekingTimer = -1;\n  #invalidPlay = false;\n  #promises = /* @__PURE__ */ new Map();\n  constructor(iframe, ctx) {\n    super(iframe);\n    this.#ctx = ctx;\n  }\n  /**\n   * Sets the player's interface language. The parameter value is an ISO 639-1 two-letter\n   * language code or a fully specified locale. For example, fr and fr-ca are both valid values.\n   * Other language input codes, such as IETF language tags (BCP 47) might also be handled properly.\n   *\n   * The interface language is used for tooltips in the player and also affects the default caption\n   * track. Note that YouTube might select a different caption track language for a particular\n   * user based on the user's individual language preferences and the availability of caption tracks.\n   *\n   * @defaultValue 'en'\n   */\n  language = \"en\";\n  color = \"red\";\n  /**\n   * Whether cookies should be enabled on the embed. This is turned off by default to be\n   * GDPR-compliant.\n   *\n   * @defaultValue `false`\n   */\n  cookies = false;\n  get currentSrc() {\n    return this.#currentSrc;\n  }\n  get type() {\n    return \"youtube\";\n  }\n  get videoId() {\n    return this.#videoId();\n  }\n  preconnect() {\n    preconnect(this.getOrigin());\n  }\n  setup() {\n    super.setup();\n    effect(this.#watchVideoId.bind(this));\n    this.#ctx.notify(\"provider-setup\", this);\n  }\n  destroy() {\n    this.#reset();\n    const message = \"provider destroyed\";\n    for (const promises of this.#promises.values()) {\n      for (const { reject } of promises) reject(message);\n    }\n    this.#promises.clear();\n  }\n  async play() {\n    return this.#remote(\"playVideo\");\n  }\n  #playFail(message) {\n    this.#getPromise(\"playVideo\")?.reject(message);\n  }\n  async pause() {\n    return this.#remote(\"pauseVideo\");\n  }\n  #pauseFail(message) {\n    this.#getPromise(\"pauseVideo\")?.reject(message);\n  }\n  setMuted(muted) {\n    if (muted) this.#remote(\"mute\");\n    else this.#remote(\"unMute\");\n  }\n  setCurrentTime(time) {\n    this.#remote(\"seekTo\", time);\n    this.#ctx.notify(\"seeking\", time);\n  }\n  setVolume(volume) {\n    this.#remote(\"setVolume\", volume * 100);\n  }\n  setPlaybackRate(rate) {\n    this.#remote(\"setPlaybackRate\", rate);\n  }\n  async loadSource(src) {\n    if (!isString(src.src)) {\n      this.#currentSrc = null;\n      this.#videoId.set(\"\");\n      return;\n    }\n    const videoId = resolveYouTubeVideoId(src.src);\n    this.#videoId.set(videoId ?? \"\");\n    this.#currentSrc = src;\n  }\n  getOrigin() {\n    return !this.cookies ? \"https://www.youtube-nocookie.com\" : \"https://www.youtube.com\";\n  }\n  #watchVideoId() {\n    this.#reset();\n    const videoId = this.#videoId();\n    if (!videoId) {\n      this.src.set(\"\");\n      return;\n    }\n    this.src.set(`${this.getOrigin()}/embed/${videoId}`);\n    this.#ctx.notify(\"load-start\");\n  }\n  buildParams() {\n    const { keyDisabled } = this.#ctx.$props, { muted, playsInline, nativeControls } = this.#ctx.$state, showControls = nativeControls();\n    return {\n      autoplay: 0,\n      cc_lang_pref: this.language,\n      cc_load_policy: showControls ? 1 : void 0,\n      color: this.color,\n      controls: showControls ? 1 : 0,\n      disablekb: !showControls || keyDisabled() ? 1 : 0,\n      enablejsapi: 1,\n      fs: 1,\n      hl: this.language,\n      iv_load_policy: showControls ? 1 : 3,\n      mute: muted() ? 1 : 0,\n      playsinline: playsInline() ? 1 : 0\n    };\n  }\n  #remote(command, arg) {\n    let promise = deferredPromise(), promises = this.#promises.get(command);\n    if (!promises) this.#promises.set(command, promises = []);\n    promises.push(promise);\n    this.postMessage({\n      event: \"command\",\n      func: command,\n      args: arg ? [arg] : void 0\n    });\n    return promise.promise;\n  }\n  onLoad() {\n    window.setTimeout(() => this.postMessage({ event: \"listening\" }), 100);\n  }\n  #onReady(trigger) {\n    this.#ctx.notify(\"loaded-metadata\");\n    this.#ctx.notify(\"loaded-data\");\n    this.#ctx.delegate.ready(void 0, trigger);\n  }\n  #onPause(trigger) {\n    this.#getPromise(\"pauseVideo\")?.resolve();\n    this.#ctx.notify(\"pause\", void 0, trigger);\n  }\n  #onTimeUpdate(time, trigger) {\n    const { duration, realCurrentTime } = this.#ctx.$state, hasEnded = this.#state === YouTubePlayerState.Ended, boundTime = hasEnded ? duration() : time;\n    this.#ctx.notify(\"time-change\", boundTime, trigger);\n    if (!hasEnded && Math.abs(boundTime - realCurrentTime()) > 1) {\n      this.#ctx.notify(\"seeking\", boundTime, trigger);\n    }\n  }\n  #onProgress(buffered, seekable, trigger) {\n    const detail = {\n      buffered: new TimeRange(0, buffered),\n      seekable\n    };\n    this.#ctx.notify(\"progress\", detail, trigger);\n    const { seeking, realCurrentTime } = this.#ctx.$state;\n    if (seeking() && buffered > realCurrentTime()) {\n      this.#onSeeked(trigger);\n    }\n  }\n  #onSeeked(trigger) {\n    const { paused, realCurrentTime } = this.#ctx.$state;\n    window.clearTimeout(this.#seekingTimer);\n    this.#seekingTimer = window.setTimeout(\n      () => {\n        this.#ctx.notify(\"seeked\", realCurrentTime(), trigger);\n        this.#seekingTimer = -1;\n      },\n      paused() ? 100 : 0\n    );\n  }\n  #onEnded(trigger) {\n    const { seeking } = this.#ctx.$state;\n    if (seeking()) this.#onSeeked(trigger);\n    this.#ctx.notify(\"pause\", void 0, trigger);\n    this.#ctx.notify(\"end\", void 0, trigger);\n  }\n  #onStateChange(state, trigger) {\n    const { paused, seeking } = this.#ctx.$state, isPlaying = state === YouTubePlayerState.Playing, isBuffering = state === YouTubePlayerState.Buffering, isPendingPlay = this.#isPending(\"playVideo\"), isPlay = paused() && (isBuffering || isPlaying);\n    if (isBuffering) this.#ctx.notify(\"waiting\", void 0, trigger);\n    if (seeking() && isPlaying) {\n      this.#onSeeked(trigger);\n    }\n    if (this.#invalidPlay && isPlaying) {\n      this.pause();\n      this.#invalidPlay = false;\n      this.setMuted(this.#ctx.$state.muted());\n      return;\n    }\n    if (!isPendingPlay && isPlay) {\n      this.#invalidPlay = true;\n      this.setMuted(true);\n      return;\n    }\n    if (isPlay) {\n      this.#getPromise(\"playVideo\")?.resolve();\n      this.#ctx.notify(\"play\", void 0, trigger);\n    }\n    switch (state) {\n      case YouTubePlayerState.Cued:\n        this.#onReady(trigger);\n        break;\n      case YouTubePlayerState.Playing:\n        this.#ctx.notify(\"playing\", void 0, trigger);\n        break;\n      case YouTubePlayerState.Paused:\n        this.#onPause(trigger);\n        break;\n      case YouTubePlayerState.Ended:\n        this.#onEnded(trigger);\n        break;\n    }\n    this.#state = state;\n  }\n  onMessage({ info }, event) {\n    if (!info) return;\n    const { title, intrinsicDuration, playbackRate } = this.#ctx.$state;\n    if (isObject(info.videoData) && info.videoData.title !== title()) {\n      this.#ctx.notify(\"title-change\", info.videoData.title, event);\n    }\n    if (isNumber(info.duration) && info.duration !== intrinsicDuration()) {\n      if (isNumber(info.videoLoadedFraction)) {\n        const buffered = info.progressState?.loaded ?? info.videoLoadedFraction * info.duration, seekable = new TimeRange(0, info.duration);\n        this.#onProgress(buffered, seekable, event);\n      }\n      this.#ctx.notify(\"duration-change\", info.duration, event);\n    }\n    if (isNumber(info.playbackRate) && info.playbackRate !== playbackRate()) {\n      this.#ctx.notify(\"rate-change\", info.playbackRate, event);\n    }\n    if (info.progressState) {\n      const { current, seekableStart, seekableEnd, loaded, duration } = info.progressState;\n      this.#onTimeUpdate(current, event);\n      this.#onProgress(loaded, new TimeRange(seekableStart, seekableEnd), event);\n      if (duration !== intrinsicDuration()) {\n        this.#ctx.notify(\"duration-change\", duration, event);\n      }\n    }\n    if (isNumber(info.volume) && isBoolean(info.muted) && !this.#invalidPlay) {\n      const detail = {\n        muted: info.muted,\n        volume: info.volume / 100\n      };\n      this.#ctx.notify(\"volume-change\", detail, event);\n    }\n    if (isNumber(info.playerState) && info.playerState !== this.#state) {\n      this.#onStateChange(info.playerState, event);\n    }\n  }\n  #reset() {\n    this.#state = -1;\n    this.#seekingTimer = -1;\n    this.#invalidPlay = false;\n  }\n  #getPromise(command) {\n    return this.#promises.get(command)?.shift();\n  }\n  #isPending(command) {\n    return Boolean(this.#promises.get(command)?.length);\n  }\n}\n\nexport { YouTubeProvider };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,mBAAO;AAGP,IAAM,qBAAqB;AAAA,EACzB,WAAW;AAAA,EACX,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AACR;AAhBA;AAkBA,IAAM,kBAAN,cAA8B,cAAc;AAAA,EAU1C,YAAY,QAAQ,KAAK;AACvB,UAAM,MAAM;AAXhB;AACE,2CAAkB;AAClB,iCAAQ,YAAY;AACpB;AACA,iCAAW,OAAO,EAAE;AACpB,+BAAS;AACT,oCAAc;AACd,sCAAgB;AAChB,qCAAe;AACf,kCAA4B,oBAAI,IAAI;AAgBpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oCAAW;AACX,iCAAQ;AAOR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mCAAU;AArBR,uBAAK,MAAO;AAAA,EACd;AAAA,EAqBA,IAAI,aAAa;AACf,WAAO,mBAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO;AACT,WAAO;AAAA,EACT;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,mBAAK,UAAL;AAAA,EACT;AAAA,EACA,aAAa;AACX,eAAW,KAAK,UAAU,CAAC;AAAA,EAC7B;AAAA,EACA,QAAQ;AACN,UAAM,MAAM;AACZ,WAAO,sBAAK,6CAAc,KAAK,IAAI,CAAC;AACpC,uBAAK,MAAK,OAAO,kBAAkB,IAAI;AAAA,EACzC;AAAA,EACA,UAAU;AACR,0BAAK,sCAAL;AACA,UAAM,UAAU;AAChB,eAAW,YAAY,mBAAK,WAAU,OAAO,GAAG;AAC9C,iBAAW,EAAE,OAAO,KAAK,SAAU,QAAO,OAAO;AAAA,IACnD;AACA,uBAAK,WAAU,MAAM;AAAA,EACvB;AAAA,EACA,MAAM,OAAO;AACX,WAAO,sBAAK,uCAAL,WAAa;AAAA,EACtB;AAAA,EAIA,MAAM,QAAQ;AACZ,WAAO,sBAAK,uCAAL,WAAa;AAAA,EACtB;AAAA,EAIA,SAAS,OAAO;AACd,QAAI,MAAO,uBAAK,uCAAL,WAAa;AAAA,QACnB,uBAAK,uCAAL,WAAa;AAAA,EACpB;AAAA,EACA,eAAe,MAAM;AACnB,0BAAK,uCAAL,WAAa,UAAU;AACvB,uBAAK,MAAK,OAAO,WAAW,IAAI;AAAA,EAClC;AAAA,EACA,UAAU,QAAQ;AAChB,0BAAK,uCAAL,WAAa,aAAa,SAAS;AAAA,EACrC;AAAA,EACA,gBAAgB,MAAM;AACpB,0BAAK,uCAAL,WAAa,mBAAmB;AAAA,EAClC;AAAA,EACA,MAAM,WAAW,KAAK;AACpB,QAAI,CAAC,SAAS,IAAI,GAAG,GAAG;AACtB,yBAAK,aAAc;AACnB,yBAAK,UAAS,IAAI,EAAE;AACpB;AAAA,IACF;AACA,UAAM,UAAU,sBAAsB,IAAI,GAAG;AAC7C,uBAAK,UAAS,IAAI,WAAW,EAAE;AAC/B,uBAAK,aAAc;AAAA,EACrB;AAAA,EACA,YAAY;AACV,WAAO,CAAC,KAAK,UAAU,qCAAqC;AAAA,EAC9D;AAAA,EAWA,cAAc;AACZ,UAAM,EAAE,YAAY,IAAI,mBAAK,MAAK,QAAQ,EAAE,OAAO,aAAa,eAAe,IAAI,mBAAK,MAAK,QAAQ,eAAe,eAAe;AACnI,WAAO;AAAA,MACL,UAAU;AAAA,MACV,cAAc,KAAK;AAAA,MACnB,gBAAgB,eAAe,IAAI;AAAA,MACnC,OAAO,KAAK;AAAA,MACZ,UAAU,eAAe,IAAI;AAAA,MAC7B,WAAW,CAAC,gBAAgB,YAAY,IAAI,IAAI;AAAA,MAChD,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,IAAI,KAAK;AAAA,MACT,gBAAgB,eAAe,IAAI;AAAA,MACnC,MAAM,MAAM,IAAI,IAAI;AAAA,MACpB,aAAa,YAAY,IAAI,IAAI;AAAA,IACnC;AAAA,EACF;AAAA,EAYA,SAAS;AACP,WAAO,WAAW,MAAM,KAAK,YAAY,EAAE,OAAO,YAAY,CAAC,GAAG,GAAG;AAAA,EACvE;AAAA,EAkFA,UAAU,EAAE,KAAK,GAAG,OAAO;AA9O7B;AA+OI,QAAI,CAAC,KAAM;AACX,UAAM,EAAE,OAAO,mBAAmB,aAAa,IAAI,mBAAK,MAAK;AAC7D,QAAI,SAAS,KAAK,SAAS,KAAK,KAAK,UAAU,UAAU,MAAM,GAAG;AAChE,yBAAK,MAAK,OAAO,gBAAgB,KAAK,UAAU,OAAO,KAAK;AAAA,IAC9D;AACA,QAAI,SAAS,KAAK,QAAQ,KAAK,KAAK,aAAa,kBAAkB,GAAG;AACpE,UAAI,SAAS,KAAK,mBAAmB,GAAG;AACtC,cAAM,aAAW,UAAK,kBAAL,mBAAoB,WAAU,KAAK,sBAAsB,KAAK,UAAU,WAAW,IAAI,UAAU,GAAG,KAAK,QAAQ;AAClI,8BAAK,2CAAL,WAAiB,UAAU,UAAU;AAAA,MACvC;AACA,yBAAK,MAAK,OAAO,mBAAmB,KAAK,UAAU,KAAK;AAAA,IAC1D;AACA,QAAI,SAAS,KAAK,YAAY,KAAK,KAAK,iBAAiB,aAAa,GAAG;AACvE,yBAAK,MAAK,OAAO,eAAe,KAAK,cAAc,KAAK;AAAA,IAC1D;AACA,QAAI,KAAK,eAAe;AACtB,YAAM,EAAE,SAAS,eAAe,aAAa,QAAQ,SAAS,IAAI,KAAK;AACvE,4BAAK,6CAAL,WAAmB,SAAS;AAC5B,4BAAK,2CAAL,WAAiB,QAAQ,IAAI,UAAU,eAAe,WAAW,GAAG;AACpE,UAAI,aAAa,kBAAkB,GAAG;AACpC,2BAAK,MAAK,OAAO,mBAAmB,UAAU,KAAK;AAAA,MACrD;AAAA,IACF;AACA,QAAI,SAAS,KAAK,MAAM,KAAK,UAAU,KAAK,KAAK,KAAK,CAAC,mBAAK,eAAc;AACxE,YAAM,SAAS;AAAA,QACb,OAAO,KAAK;AAAA,QACZ,QAAQ,KAAK,SAAS;AAAA,MACxB;AACA,yBAAK,MAAK,OAAO,iBAAiB,QAAQ,KAAK;AAAA,IACjD;AACA,QAAI,SAAS,KAAK,WAAW,KAAK,KAAK,gBAAgB,mBAAK,SAAQ;AAClE,4BAAK,8CAAL,WAAoB,KAAK,aAAa;AAAA,IACxC;AAAA,EACF;AAYF;AAvQE;AACA;AACA;AACA;AACA;AACA;AACA;AATF;AA8DE,cAAS,SAAC,SAAS;AAhFrB;AAiFI,8BAAK,2CAAL,WAAiB,iBAAjB,mBAA+B,OAAO;AACxC;AAIA,eAAU,SAAC,SAAS;AAtFtB;AAuFI,8BAAK,2CAAL,WAAiB,kBAAjB,mBAAgC,OAAO;AACzC;AA4BA,kBAAa,WAAG;AACd,wBAAK,sCAAL;AACA,QAAM,UAAU,mBAAK,UAAL;AAChB,MAAI,CAAC,SAAS;AACZ,SAAK,IAAI,IAAI,EAAE;AACf;AAAA,EACF;AACA,OAAK,IAAI,IAAI,GAAG,KAAK,UAAU,CAAC,UAAU,OAAO,EAAE;AACnD,qBAAK,MAAK,OAAO,YAAY;AAC/B;AAkBA,YAAO,SAAC,SAAS,KAAK;AACpB,MAAI,UAAU,gBAAgB,GAAG,WAAW,mBAAK,WAAU,IAAI,OAAO;AACtE,MAAI,CAAC,SAAU,oBAAK,WAAU,IAAI,SAAS,WAAW,CAAC,CAAC;AACxD,WAAS,KAAK,OAAO;AACrB,OAAK,YAAY;AAAA,IACf,OAAO;AAAA,IACP,MAAM;AAAA,IACN,MAAM,MAAM,CAAC,GAAG,IAAI;AAAA,EACtB,CAAC;AACD,SAAO,QAAQ;AACjB;AAIA,aAAQ,SAAC,SAAS;AAChB,qBAAK,MAAK,OAAO,iBAAiB;AAClC,qBAAK,MAAK,OAAO,aAAa;AAC9B,qBAAK,MAAK,SAAS,MAAM,QAAQ,OAAO;AAC1C;AACA,aAAQ,SAAC,SAAS;AAlKpB;AAmKI,8BAAK,2CAAL,WAAiB,kBAAjB,mBAAgC;AAChC,qBAAK,MAAK,OAAO,SAAS,QAAQ,OAAO;AAC3C;AACA,kBAAa,SAAC,MAAM,SAAS;AAC3B,QAAM,EAAE,UAAU,gBAAgB,IAAI,mBAAK,MAAK,QAAQ,WAAW,mBAAK,YAAW,mBAAmB,OAAO,YAAY,WAAW,SAAS,IAAI;AACjJ,qBAAK,MAAK,OAAO,eAAe,WAAW,OAAO;AAClD,MAAI,CAAC,YAAY,KAAK,IAAI,YAAY,gBAAgB,CAAC,IAAI,GAAG;AAC5D,uBAAK,MAAK,OAAO,WAAW,WAAW,OAAO;AAAA,EAChD;AACF;AACA,gBAAW,SAAC,UAAU,UAAU,SAAS;AACvC,QAAM,SAAS;AAAA,IACb,UAAU,IAAI,UAAU,GAAG,QAAQ;AAAA,IACnC;AAAA,EACF;AACA,qBAAK,MAAK,OAAO,YAAY,QAAQ,OAAO;AAC5C,QAAM,EAAE,SAAS,gBAAgB,IAAI,mBAAK,MAAK;AAC/C,MAAI,QAAQ,KAAK,WAAW,gBAAgB,GAAG;AAC7C,0BAAK,yCAAL,WAAe;AAAA,EACjB;AACF;AACA,cAAS,SAAC,SAAS;AACjB,QAAM,EAAE,QAAQ,gBAAgB,IAAI,mBAAK,MAAK;AAC9C,SAAO,aAAa,mBAAK,cAAa;AACtC,qBAAK,eAAgB,OAAO;AAAA,IAC1B,MAAM;AACJ,yBAAK,MAAK,OAAO,UAAU,gBAAgB,GAAG,OAAO;AACrD,yBAAK,eAAgB;AAAA,IACvB;AAAA,IACA,OAAO,IAAI,MAAM;AAAA,EACnB;AACF;AACA,aAAQ,SAAC,SAAS;AAChB,QAAM,EAAE,QAAQ,IAAI,mBAAK,MAAK;AAC9B,MAAI,QAAQ,EAAG,uBAAK,yCAAL,WAAe;AAC9B,qBAAK,MAAK,OAAO,SAAS,QAAQ,OAAO;AACzC,qBAAK,MAAK,OAAO,OAAO,QAAQ,OAAO;AACzC;AACA,mBAAc,SAAC,OAAO,SAAS;AAzMjC;AA0MI,QAAM,EAAE,QAAQ,QAAQ,IAAI,mBAAK,MAAK,QAAQ,YAAY,UAAU,mBAAmB,SAAS,cAAc,UAAU,mBAAmB,WAAW,gBAAgB,sBAAK,0CAAL,WAAgB,cAAc,SAAS,OAAO,MAAM,eAAe;AACzO,MAAI,YAAa,oBAAK,MAAK,OAAO,WAAW,QAAQ,OAAO;AAC5D,MAAI,QAAQ,KAAK,WAAW;AAC1B,0BAAK,yCAAL,WAAe;AAAA,EACjB;AACA,MAAI,mBAAK,iBAAgB,WAAW;AAClC,SAAK,MAAM;AACX,uBAAK,cAAe;AACpB,SAAK,SAAS,mBAAK,MAAK,OAAO,MAAM,CAAC;AACtC;AAAA,EACF;AACA,MAAI,CAAC,iBAAiB,QAAQ;AAC5B,uBAAK,cAAe;AACpB,SAAK,SAAS,IAAI;AAClB;AAAA,EACF;AACA,MAAI,QAAQ;AACV,gCAAK,2CAAL,WAAiB,iBAAjB,mBAA+B;AAC/B,uBAAK,MAAK,OAAO,QAAQ,QAAQ,OAAO;AAAA,EAC1C;AACA,UAAQ,OAAO;AAAA,IACb,KAAK,mBAAmB;AACtB,4BAAK,wCAAL,WAAc;AACd;AAAA,IACF,KAAK,mBAAmB;AACtB,yBAAK,MAAK,OAAO,WAAW,QAAQ,OAAO;AAC3C;AAAA,IACF,KAAK,mBAAmB;AACtB,4BAAK,wCAAL,WAAc;AACd;AAAA,IACF,KAAK,mBAAmB;AACtB,4BAAK,wCAAL,WAAc;AACd;AAAA,EACJ;AACA,qBAAK,QAAS;AAChB;AAoCA,WAAM,WAAG;AACP,qBAAK,QAAS;AACd,qBAAK,eAAgB;AACrB,qBAAK,cAAe;AACtB;AACA,gBAAW,SAAC,SAAS;AAtRvB;AAuRI,UAAO,wBAAK,WAAU,IAAI,OAAO,MAA1B,mBAA6B;AACtC;AACA,eAAU,SAAC,SAAS;AAzRtB;AA0RI,SAAO,SAAQ,wBAAK,WAAU,IAAI,OAAO,MAA1B,mBAA6B,MAAM;AACpD;", "names": []}